import android.net.Uri

/**
 * Simple test to verify the filename extraction function works correctly
 */
fun main() {
    // Test URLs
    val testUrls = listOf(
        "https://example.com/animations/galaxy_effect.mp4",
        "https://cdn.example.com/videos/lightning_animation.mp4",
        "https://storage.googleapis.com/bucket/fire_effect.mp4",
        "https://example.com/path/with/spaces/space%20animation.mp4",
        "https://example.com/no-extension-file",
        "https://example.com/",
        "",
        "https://example.com/path/with/query?param=value&file=test.mp4",
        "https://example.com/path/with/fragment#section.mp4"
    )
    
    println("Testing filename extraction:")
    println("=" * 50)
    
    for (url in testUrls) {
        val filename = getFileNameFromUrl(url)
        println("URL: $url")
        println("Filename: $filename")
        println("-" * 30)
    }
}

/**
 * Extracts a safe filename from a given URL.
 * Falls back to a unique name based on the URL's hashcode if a name cannot be derived.
 */
fun getFileNameFromUrl(url: String): String {
    return try {
        // Use Android's Uri class to reliably get the last path segment.
        val fileName = Uri.parse(url).lastPathSegment
        // Basic sanitization and fallback
        if (fileName.isNullOrEmpty() || fileName.length < 3) {
            // If no filename, create one from the URL's hashcode
            "${url.hashCode()}.mp4"
        } else {
            // Ensure it doesn't contain path separators
            fileName.replace("/", "_")
        }
    } catch (e: Exception) {
        // Fallback for any parsing error
        "${url.hashCode()}.mp4"
    }
}
