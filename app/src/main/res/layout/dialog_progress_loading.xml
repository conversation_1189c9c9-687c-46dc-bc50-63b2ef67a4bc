<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="24dp"
    android:background="@android:color/transparent"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <ProgressBar
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminate="true" />

    <TextView
        android:id="@+id/loading_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Loading video..."
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:layout_marginTop="16dp" />

    <ProgressBar
        android:id="@+id/progress_bar_horizontal"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        style="?android:attr/progressBarStyleHorizontal"
        android:max="100"
        android:progress="0"
        android:progressTint="@android:color/white"
        android:progressBackgroundTint="@android:color/darker_gray" />

    <TextView
        android:id="@+id/progress_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="0%"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:layout_marginTop="8dp" />

</LinearLayout>
