<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.BatteryOne" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:statusBarColor">#ffffff</item>
        <item name="android:navigationBarColor">#ffffff</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:textColorHint">#bfbfbf</item>
        <item name="alarm">#d5d5d5</item>
        <item name="alarm_selector">#cdcdcd</item>
        <item name="black">#ff000000</item>
        <item name="colorr">#109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorControlActivated">#bababa</item>
        <item name="colorControlHighlight">#ffdddddd</item>
        <item name="colorOnSurface">#bfbfbf</item>
        <item name="colorPrimary">#bfbfbf</item>
        <item name="fontFamily">@font/ubuntu</item>
        <item name="graph_text">#1e1e1e</item>
        <item name="grey">#ffffff</item>
        <item name="grey_ads">#FEFEFE</item>
        <item name="grey_lighter">#fbfbfb</item>
        <item name="grey_pressed">#e4e4e4</item>
        <item name="night">#595959</item>
        <item name="second_color_icon_starting_page">#e4e4e4</item>
        <item name="textColorTextInput">#ff000000</item>
        <item name="unselected_button">#989898</item>
        <item name="white">#f4f4f4</item>
        <item name="white_ads">#faf0f0</item>

    </style>
    <style name="Theme.BatteryOne.AppBarOverlay" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
    </style>
    <style name="Theme.BatteryOne.NoActionBar" parent="@style/Theme.BatteryOne">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="StartingTheme.PopupOverlay" parent="@style/ThemeOverlay.AppCompat.Light">
    </style>
    <style name="BottomNavigationViewTextStyle" parent="">
        <item name="android:textSize">13sp</item>
        <item name="fontFamily">@font/ubuntu</item>
    </style>
    <style name="LightTheme" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColorHint">#bfbfbf</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:statusBarColor">#ffffff</item>
        <item name="android:navigationBarColor">#ffffff</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="alarm">#d5d5d5</item>
        <item name="alarm_selector">#cdcdcd</item>
        <item name="black">#ff000000</item>
        <item name="colorr">#109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorControlActivated">#bababa</item>
        <item name="colorControlHighlight">#ffdddddd</item>
        <item name="colorOnSurface">#bfbfbf</item>
        <item name="colorPrimary">#bfbfbf</item>
        <item name="fontFamily">@font/ubuntu</item>
        <item name="graph_text">#1e1e1e</item>
        <item name="grey">#ffffff</item>
        <item name="grey_ads">#FEFEFE</item>
        <item name="grey_lighter">#fbfbfb</item>
        <item name="grey_pressed">#e4e4e4</item>
        <item name="night">#595959</item>
        <item name="second_color_icon_starting_page">#e4e4e4</item>
        <item name="textColorTextInput">#ff000000</item>
        <item name="unselected_button">#989898</item>
        <item name="white">#f4f4f4</item>
        <item name="white_ads">#faf0f0</item>

    </style>
    <style name="LightThemeInverted" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColorHint">#bfbfbf</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:statusBarColor">#f4f4f4</item>
        <item name="android:navigationBarColor">#f4f4f4</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="alarm">#d5d5d5</item>
        <item name="alarm_selector">#cdcdcd</item>
        <item name="black">#ff000000</item>
        <item name="colorr">#109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorControlActivated">#bababa</item>
        <item name="colorControlHighlight">#ffdddddd</item>
        <item name="colorOnSurface">#bfbfbf</item>
        <item name="colorPrimary">#bfbfbf</item>
        <item name="fontFamily">@font/ubuntu</item>
        <item name="graph_text">#1e1e1e</item>
        <item name="grey">#f4f4f4</item>
        <item name="grey_ads">#faf0f0</item>
        <item name="grey_lighter">#f4f4f4</item>
        <item name="grey_pressed">#e4e4e4</item>
        <item name="night">#595959</item>
        <item name="second_color_icon_starting_page">#e4e4e4</item>
        <item name="textColorTextInput">#ff000000</item>
        <item name="unselected_button">#989898</item>
        <item name="white">#ffffff</item>
        <item name="white_ads">#FEFEFE</item>

    </style>
    <style name="GreyTheme" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColorHint">#9c9c9c</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:statusBarColor">#bdbdbd</item>
        <item name="android:navigationBarColor">#bdbdbd</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="alarm">#c5c5c5</item>
        <item name="alarm_selector">#838383</item>
        <item name="black">#000000</item>
        <item name="colorr">#109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorControlActivated">#bababa</item>
        <item name="colorControlHighlight">#888888</item>
        <item name="colorOnSurface">#9c9c9c</item>
        <item name="colorPrimary">#9c9c9c</item>
        <item name="fontFamily">@font/ubuntu</item>
        <item name="graph_text">#050505</item>
        <item name="grey">#bdbdbd</item>
        <item name="grey_ads">#bab8b8</item>
        <item name="grey_lighter">#c8c8c8</item>
        <item name="grey_pressed">#919191</item>
        <item name="night">#787878</item>
        <item name="second_color_icon_starting_page">#e4e4e4</item>
        <item name="textColorTextInput">#ffdddddd</item>
        <item name="unselected_button">#838383</item>
        <item name="white">#aaaaaa</item>
        <item name="white_ads">#a69d9d</item>

    </style>
    <style name="GreyThemeInverted" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColorHint">#888888</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:statusBarColor">#aaaaaa</item>
        <item name="android:navigationBarColor">#aaaaaa</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="alarm">#c5c5c5</item>
        <item name="alarm_selector">#838383</item>
        <item name="black">#000000</item>
        <item name="colorr">#109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorControlActivated">#bababa</item>
        <item name="colorControlHighlight">#888888</item>
        <item name="colorOnSurface">#c5c5c5</item>
        <item name="colorPrimary">#888888</item>
        <item name="fontFamily">@font/ubuntu</item>
        <item name="graph_text">#050505</item>
        <item name="grey">#aaaaaa</item>
        <item name="grey_ads">#a8a7a7</item>
        <item name="grey_lighter">#aaaaaa</item>
        <item name="grey_pressed">#919191</item>
        <item name="night">#787878</item>
        <item name="second_color_icon_starting_page">#e4e4e4</item>
        <item name="textColorTextInput">#c5c5c5</item>
        <item name="unselected_button">#838383</item>
        <item name="white">#bdbdbd</item>
        <item name="white_ads">#bab1b1</item>

    </style>
    <style name="AmoledTheme" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColorHint">#888888</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:statusBarColor">#000000</item>
        <item name="android:navigationBarColor">#000000</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="alarm">#c5c5c5</item>
        <item name="alarm_selector">#555555</item>
        <item name="black">#c5c5c5</item>
        <item name="colorr">#109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorControlActivated">#bababa</item>
        <item name="colorControlHighlight">#3c3c3c</item>
        <item name="colorOnSurface">#c5c5c5</item>
        <item name="colorPrimary">#888888</item>
        <item name="fontFamily">@font/ubuntu</item>
        <item name="graph_text">#c5c5c5</item>
        <item name="grey">#000000</item>
        <item name="grey_ads">#0d0d0d</item>
        <item name="grey_lighter">#101010</item>
        <item name="grey_pressed">#404040</item>
        <item name="night">#888888</item>
        <item name="second_color_icon_starting_page">#9f9f9f</item>
        <item name="textColorTextInput">#c5c5c5</item>
        <item name="unselected_button">#838383</item>
        <item name="white">#151515</item>
        <item name="white_ads">#171515</item>

    </style>
    <style name="AmoledThemeInverted" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColorHint">#888888</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:statusBarColor">#151515</item>
        <item name="android:navigationBarColor">#151515</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="alarm">#c5c5c5</item>
        <item name="alarm_selector">#555555</item>
        <item name="black">#c5c5c5</item>
        <item name="colorr">#109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorControlActivated">#bababa</item>
        <item name="colorControlHighlight">#3c3c3c</item>
        <item name="colorOnSurface">#c5c5c5</item>
        <item name="colorPrimary">#888888</item>
        <item name="fontFamily">@font/ubuntu</item>
        <item name="graph_text">#c5c5c5</item>
        <item name="grey">#151515</item>
        <item name="grey_ads">#121111</item>
        <item name="grey_lighter">#101010</item>
        <item name="grey_pressed">#404040</item>
        <item name="night">#888888</item>
        <item name="second_color_icon_starting_page">#9f9f9f</item>
        <item name="textColorTextInput">#c5c5c5</item>
        <item name="unselected_button">#838383</item>
        <item name="white">#000000</item>
        <item name="white_ads">#0f0f0f</item>

    </style>
    <style name="BlackTheme" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColorHint">#9c9c9c</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:statusBarColor">#1b1b1b</item>
        <item name="android:navigationBarColor">#1b1b1b</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="alarm">#ffdddddd</item>
        <item name="alarm_selector">#737373</item>
        <item name="black">#ffdddddd</item>
        <item name="colorr">#109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorControlActivated">#7a7a7a</item>
        <item name="colorControlHighlight">#3c3c3c</item>
        <item name="colorOnSurface">#9c9c9c</item>
        <item name="colorPrimary">#9c9c9c</item>
        <item name="fontFamily">@font/ubuntu</item>
        <item name="graph_text">#ffdddddd</item>
        <item name="grey">#1b1b1b</item>
        <item name="grey_ads">#1c1c1c</item>
        <item name="grey_lighter">#1e1e1e</item>
        <item name="grey_pressed">#101010</item>
        <item name="night">#a3a3a3</item>
        <item name="second_color_icon_starting_page">#9f9f9f</item>
        <item name="textColorTextInput">#ffdddddd</item>
        <item name="unselected_button">#7a7a7a</item>
        <item name="white">#282828</item>
        <item name="white_ads">#292727</item>

    </style>
    <style name="BlackThemeInverted" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:textColorHint">#9c9c9c</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:statusBarColor">#282828</item>
        <item name="android:navigationBarColor">#282828</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="alarm">#ffdddddd</item>
        <item name="alarm_selector">#737373</item>
        <item name="black">#ffdddddd</item>
        <item name="colorr">#109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorControlActivated">#7a7a7a</item>
        <item name="colorControlHighlight">#3c3c3c</item>
        <item name="colorOnSurface">#9c9c9c</item>
        <item name="colorPrimary">#9c9c9c</item>
        <item name="fontFamily">@font/ubuntu</item>
        <item name="graph_text">#ffdddddd</item>
        <item name="grey">#282828</item>
        <item name="grey_ads">#292929</item>
        <item name="grey_lighter">#303030</item>
        <item name="grey_pressed">#101010</item>
        <item name="night">#a3a3a3</item>
        <item name="second_color_icon_starting_page">#9f9f9f</item>
        <item name="textColorTextInput">#ffdddddd</item>
        <item name="unselected_button">#7a7a7a</item>
        <item name="white">#1b1b1b</item>
        <item name="white_ads">#1c1b1b</item>
    </style>

    <style name="Theme.BatteryOne.Splash" parent="Theme.SplashScreen">
        <item name="windowSplashScreenBackground">?attr/grey</item>
        <item name="windowSplashScreenAnimatedIcon">@mipmap/ic_launcher</item>
        <item name="postSplashScreenTheme">@style/Theme.BatteryOne.NoActionBar</item>
        <item name="windowSplashScreenAnimationDuration">0</item>
        <item name="windowSplashScreenIconBackgroundColor">@android:color/transparent</item>
        <item name="android:windowBackground">?attr/grey</item>
    </style>

    <!-- Color Styles -->
    <style name="Red" parent="">
        <item name="color_poz">#75ff5151</item>
        <item name="color_red">#ff9800</item>
        <item name="colorr">#ff5151</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="Telo" parent="">
        <item name="color_poz">#75ecc19c</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#ecc19c</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="Gold" parent="">
        <item name="color_poz">#75f3ca20</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#f3ca20</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="Green" parent="">
        <item name="color_poz">#75109d58</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#109d58</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="Light_blue" parent="">
        <item name="color_poz">#75a2d5c6</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#a2d5c6</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="Light_green" parent="">
        <item name="color_poz">#75a8c66c</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#a8c66c</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="Blue" parent="">
        <item name="color_poz">#758744ff</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#8744ff</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="Night_blue" parent="">
        <item name="color_poz">#753b4d61</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#3b4d61</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="Orange" parent="">
        <item name="color_poz">#75ff6e40</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#ff6e40</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="Pink" parent="">
        <item name="color_poz">#75d9a5b3</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#d9a5b3</item>
        <item name="colorSurface">#ffffff</item>
    </style>

    <style name="color_1" parent="">
        <item name="color_poz">#756b8e23</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#6b8e23</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="color_10" parent="">
        <item name="color_poz">#bfa0522d</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#a0522d</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="color_2" parent="">
        <item name="color_poz">#7587cefa</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#87cefa</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="color_3" parent="">
        <item name="color_poz">#75da70d6</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#da70d6</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="color_4" parent="">
        <item name="color_poz">#9700ff00</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#00ff00</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="color_5" parent="">
        <item name="color_poz">#8100ffff</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#10ffff</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="color_6" parent="">
        <item name="color_poz">#b0ffa500</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#ffa500</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="color_7" parent="">
        <item name="color_poz">#a3ff1493</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#ff1493</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="color_8" parent="">
        <item name="color_poz">#a4ff0000</item>
        <item name="color_red">#ffa100</item>
        <item name="colorr">#ff0000</item>
        <item name="colorSurface">#ffffff</item>
    </style>
    <style name="color_9" parent="">
        <item name="color_poz">#b74831d5</item>
        <item name="color_red">#e90000</item>
        <item name="colorr">#4831d5</item>
        <item name="colorSurface">#ffffff</item>
    </style>
</resources>