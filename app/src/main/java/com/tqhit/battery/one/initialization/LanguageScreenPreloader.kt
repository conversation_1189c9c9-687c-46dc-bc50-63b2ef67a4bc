package com.tqhit.battery.one.initialization

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.Log
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import com.tqhit.battery.one.R
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Pre-loads language screen resources during splash screen to improve transition performance.
 * 
 * This class handles pre-loading of:
 * - Drawable resources (language button backgrounds)
 * - Font resources (Ubuntu font)
 * - String resources (language names and UI text)
 * - Layout inflation preparation
 * 
 * Pre-loading runs in background threads to avoid blocking splash screen operations.
 */
@Singleton
class LanguageScreenPreloader @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    companion object {
        private const val TAG = "LanguageScreenPreloader"
    }
    
    // Pre-loaded resources cache
    private var isPreloadingComplete = false
    private var preloadedDrawables: Map<String, Drawable?> = emptyMap()
    private var preloadedStrings: Map<String, String> = emptyMap()
    private var preloadedFont: Any? = null
    
    /**
     * Start pre-loading language screen resources in background
     */
    suspend fun preloadLanguageScreenResources() {
        if (isPreloadingComplete) {
            Log.d(TAG, "LANGUAGE_PRELOAD: Resources already pre-loaded, skipping")
            return
        }
        
        val preloadStartTime = System.currentTimeMillis()
        Log.d(TAG, "LANGUAGE_PRELOAD: Starting language screen resource pre-loading")
        
        withContext(Dispatchers.IO) {
            try {
                // Pre-load drawable resources
                preloadDrawableResources()

                // Pre-load string resources
                preloadStringResources()

                // Pre-load font resources
                preloadFontResources()

                isPreloadingComplete = true
                val preloadDuration = System.currentTimeMillis() - preloadStartTime
                Log.d(TAG, "LANGUAGE_PRELOAD: Resource pre-loading completed in ${preloadDuration}ms")

            } catch (e: Exception) {
                Log.e(TAG, "LANGUAGE_PRELOAD: Error during resource pre-loading", e)
                // Continue anyway - pre-loading is an optimization, not critical
                isPreloadingComplete = true // Mark as complete even on error to avoid blocking
            }
        }
    }
    
    /**
     * Pre-load drawable resources used in language selection
     */
    private suspend fun preloadDrawableResources() {
        val drawableResources = mapOf(
            "grey_block_line" to R.drawable.grey_block_line,
            "grey_block_line_up" to R.drawable.grey_block_line_up,
            "grey_block_line_down" to R.drawable.grey_block_line_down,
            "grey_block_selected_color" to R.drawable.grey_block_selected_color,
            "grey_block_selected_line_up" to R.drawable.grey_block_selected_line_up,
            "grey_block_selected_line_down" to R.drawable.grey_block_selected_line_down,
            "grey_block_selected_color_line_up" to R.drawable.grey_block_selected_color_line_up,
            "grey_block_selected_color_line_down" to R.drawable.grey_block_selected_color_line_down
        )

        val loadedDrawables = mutableMapOf<String, Drawable?>()

        for ((name, resourceId) in drawableResources) {
            try {
                val drawable = ContextCompat.getDrawable(context, resourceId)
                loadedDrawables[name] = drawable
            } catch (e: Exception) {
                Log.w(TAG, "LANGUAGE_PRELOAD: Failed to load drawable: $name", e)
                loadedDrawables[name] = null
            }
        }

        preloadedDrawables = loadedDrawables
    }
    
    /**
     * Pre-load string resources used in language selection
     */
    private suspend fun preloadStringResources() {
        val stringResources = mapOf(
            "select_language_title" to R.string.select_language_title,
            "select_language_subtitle" to R.string.select_language_subtitle,
            "next" to R.string.next,
            "Germany_lang" to R.string.Germany_lang,
            "Dutch_lang" to R.string.Dutch_lang,
            "English_lang" to R.string.English_lang,
            "Spanish_lang" to R.string.Spanish_lang,
            "French_lang" to R.string.French_lang,
            "Italy_lang" to R.string.Italy_lang,
            "Hungarian_lang" to R.string.Hungarian_lang,
            "Poland_lang" to R.string.Poland_lang,
            "Portuguese_lang" to R.string.Portuguese_lang,
            "Romanian_lang" to R.string.Romanian_lang,
            "Turkish_lang" to R.string.Turkish_lang,
            "Russian_lang" to R.string.Russian_lang,
            "Ukraine_lang" to R.string.Ukraine_lang,
            "arabic_lang" to R.string.arabic_lang,
            "_hinese_traditional_lang_res_0x7f130305" to R.string._hinese_traditional_lang_res_0x7f130305
        )

        val loadedStrings = mutableMapOf<String, String>()

        for ((name, resourceId) in stringResources) {
            try {
                val string = context.getString(resourceId)
                loadedStrings[name] = string
            } catch (e: Exception) {
                Log.w(TAG, "LANGUAGE_PRELOAD: Failed to load string: $name", e)
                loadedStrings[name] = ""
            }
        }

        preloadedStrings = loadedStrings
    }
    
    /**
     * Pre-load font resources used in language selection
     */
    private suspend fun preloadFontResources() {
        try {
            // Pre-load Ubuntu font
            val ubuntuFont = ResourcesCompat.getFont(context, R.font.ubuntu)
            preloadedFont = ubuntuFont
        } catch (e: Exception) {
            Log.w(TAG, "LANGUAGE_PRELOAD: Failed to load Ubuntu font", e)
            preloadedFont = null
        }
    }
    
    /**
     * Check if pre-loading is complete
     */
    fun isPreloadingComplete(): Boolean = isPreloadingComplete
    
    /**
     * Get pre-loaded drawable by name
     */
    fun getPreloadedDrawable(name: String): Drawable? = preloadedDrawables[name]
    
    /**
     * Get pre-loaded string by name
     */
    fun getPreloadedString(name: String): String? = preloadedStrings[name]
    
    /**
     * Get pre-loaded font
     */
    fun getPreloadedFont(): Any? = preloadedFont
    
    /**
     * Reset pre-loading state for testing
     */
    fun resetPreloadingState() {
        Log.d(TAG, "LANGUAGE_PRELOAD: Resetting pre-loading state")
        isPreloadingComplete = false
        preloadedDrawables = emptyMap()
        preloadedStrings = emptyMap()
        preloadedFont = null
    }
    
    /**
     * Get pre-loading statistics for debugging
     */
    fun getPreloadingStats(): String {
        return "Pre-loading complete: $isPreloadingComplete, " +
                "Drawables loaded: ${preloadedDrawables.size}, " +
                "Strings loaded: ${preloadedStrings.size}, " +
                "Font loaded: ${preloadedFont != null}"
    }
}
