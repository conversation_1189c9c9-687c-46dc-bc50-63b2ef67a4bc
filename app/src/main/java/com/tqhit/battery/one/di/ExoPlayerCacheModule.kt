package com.tqhit.battery.one.di

import android.content.Context
import androidx.media3.database.DatabaseProvider
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import com.tqhit.battery.one.utils.BatteryLogger
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import java.io.File
import javax.inject.Singleton

/**
 * Dagger Hilt module for ExoPlayer cache dependency injection.
 * Provides bindings for all ExoPlayer cache related components.
 * 
 * Following SOLID principles:
 * - Single Responsibility: Only handles ExoPlayer cache DI configuration
 * - Open/Closed: Extensible for additional cache components
 * - Dependency Inversion: Provides abstractions and implementations
 */
@Module
@InstallIn(SingletonComponent::class)
object ExoPlayerCacheModule {
    
    private const val TAG = "ExoPlayerCacheModule"
    private const val CACHE_SIZE = 150 * 1024 * 1024L // 150MB
    private const val CACHE_DIR_NAME = "exoplayer_cache"
    
    /**
     * Provides DatabaseProvider singleton.
     * Required for cache metadata storage.
     * 
     * @param context Application context for database operations
     * @return DatabaseProvider instance
     */
    @Provides
    @Singleton
    fun provideDatabaseProvider(
        @ApplicationContext context: Context
    ): DatabaseProvider {
        return try {
            BatteryLogger.d(TAG, "Creating StandaloneDatabaseProvider for cache metadata")
            StandaloneDatabaseProvider(context)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error creating DatabaseProvider", e)
            throw e
        }
    }
    
    /**
     * Provides SimpleCache singleton.
     * Configure with 150MB cache size using LeastRecentlyUsedCacheEvictor.
     * 
     * @param context Application context for cache directory
     * @param databaseProvider Database provider for cache metadata
     * @return SimpleCache instance
     */
    @Provides
    @Singleton
    fun provideSimpleCache(
        @ApplicationContext context: Context,
        databaseProvider: DatabaseProvider
    ): SimpleCache {
        return try {
            val cacheDir = File(context.cacheDir, CACHE_DIR_NAME)
            val cacheEvictor = LeastRecentlyUsedCacheEvictor(CACHE_SIZE)
            
            BatteryLogger.d(TAG, "Creating SimpleCache with ${CACHE_SIZE / (1024 * 1024)}MB size at: ${cacheDir.absolutePath}")
            
            SimpleCache(cacheDir, cacheEvictor, databaseProvider)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error creating SimpleCache", e)
            throw e
        }
    }
    
    /**
     * Provides CacheDataSource.Factory singleton.
     * Combine cache with network data source for seamless caching.
     * 
     * @param context Application context for data source factory
     * @param cache SimpleCache instance
     * @return CacheDataSource.Factory instance
     */
    @Provides
    @Singleton
    fun provideCacheDataSourceFactory(
        @ApplicationContext context: Context,
        cache: SimpleCache
    ): CacheDataSource.Factory {
        return try {
            val httpDataSourceFactory = DefaultHttpDataSource.Factory()
                .setUserAgent("TJ_BatteryOne")
                .setConnectTimeoutMs(30_000)
                .setReadTimeoutMs(30_000)
            
            val upstreamDataSourceFactory = DefaultDataSource.Factory(context, httpDataSourceFactory)
            
            BatteryLogger.d(TAG, "Creating CacheDataSource.Factory with network fallback")
            
            CacheDataSource.Factory()
                .setCache(cache)
                .setUpstreamDataSourceFactory(upstreamDataSourceFactory)
                .setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error creating CacheDataSource.Factory", e)
            throw e
        }
    }
}
