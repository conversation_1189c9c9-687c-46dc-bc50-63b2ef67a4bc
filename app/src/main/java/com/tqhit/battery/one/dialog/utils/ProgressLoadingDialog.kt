package com.tqhit.battery.one.dialog.utils

import android.content.Context
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import androidx.core.view.isVisible
import com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.DialogProgressLoadingBinding

class ProgressLoadingDialog(
    context: Context,
    private val message: String = "Loading..."
) : AdLibBaseDialog<DialogProgressLoadingBinding>(context) {
    
    override val binding by lazy { DialogProgressLoadingBinding.inflate(layoutInflater) }
    
    private var currentProgress = 0
    
    override fun initWindow() {
        super.initWindow()
        setCanceledOnTouchOutside(false)
        setCancelable(false)
    }

    override fun setupUI() {
        super.setupUI()
        binding.loadingMessage.text = message
        updateProgress(0)
    }

    /**
     * Updates the progress percentage with smooth animation
     * @param progress Progress value from 0 to 100
     */
    fun updateProgress(progress: Int) {
        val clampedProgress = progress.coerceIn(0, 100)
        
        if (clampedProgress != currentProgress) {
            currentProgress = clampedProgress
            
            // Update progress bar
            binding.progressBarHorizontal.progress = clampedProgress
            
            // Update progress text
            binding.progressText.text = "${clampedProgress}%"
            
            // Add subtle animation for progress text
            val fadeAnimation = AnimationUtils.loadAnimation(context, android.R.anim.fade_in)
            fadeAnimation.duration = 200
            binding.progressText.startAnimation(fadeAnimation)
        }
    }

    /**
     * Updates the loading message
     * @param newMessage The new message to display
     */
    fun updateMessage(newMessage: String) {
        binding.loadingMessage.text = newMessage
    }

    /**
     * Shows or hides the horizontal progress bar
     * @param show Whether to show the progress bar
     */
    fun showProgressBar(show: Boolean) {
        binding.progressBarHorizontal.isVisible = show
        binding.progressText.isVisible = show
    }

    override fun dismiss() {
        // Cancel any running animations before dismissing
        binding.progressText.clearAnimation()
        super.dismiss()
    }
}
