package com.tqhit.battery.one.activity.animation

import android.widget.Toast
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.databinding.ActivityAnimationBinding
import dagger.hilt.android.AndroidEntryPoint
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.datasource.cache.CacheDataSource
import com.tqhit.battery.one.R
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.BatteryManager
import android.os.Handler
import android.os.Looper
import android.view.View
import com.tqhit.battery.one.utils.DateTimeUtils
import kotlinx.coroutines.launch
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.util.UnstableApi
import androidx.media3.ui.AspectRatioFrameLayout
import com.tqhit.adlib.sdk.firebase.FirebaseRemoteConfigHelper
import com.tqhit.battery.one.activity.animation.onCompleted.PostAnimationCompleteActivity
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.ads.core.ApplovinRewardedAdManager
import com.tqhit.battery.one.dialog.utils.ProgressLoadingDialog
import com.tqhit.battery.one.repository.AppRepository
import com.tqhit.battery.one.utils.BatteryLogger
import com.tqhit.battery.one.utils.OverlayPermissionUtils
import com.tqhit.battery.one.utils.VideoUtils
import com.tqhit.battery.one.viewmodel.animation.AnimationViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import java.io.File
import javax.inject.Inject

@AndroidEntryPoint
class AnimationActivity : AdLibBaseActivity<ActivityAnimationBinding>() {
    override val binding by lazy { ActivityAnimationBinding.inflate(layoutInflater) }

    @Inject lateinit var remoteConfigHelper: FirebaseRemoteConfigHelper
    @Inject lateinit var applovinRewardedAdManager: ApplovinRewardedAdManager
    private val animationViewModel: AnimationViewModel by viewModels()
    private var mediaUrl = ""
    @Inject
    lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager

    private var player: ExoPlayer? = null
    private var progressLoadingDialog: ProgressLoadingDialog? = null
    private var progressMonitoringJob: Job? = null
    private val timeHandler = Handler(Looper.getMainLooper())
    private val timeRunnable = object : Runnable {
        override fun run() {
            updateTimeAndDate()
            timeHandler.postDelayed(this, 60_000L)
        }
    }
    private val batteryReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val level = intent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
            if (level >= 0) {
                binding.batteryPercent.text = "$level%"
            }
        }
    }

    private var overlayPermissionLauncher: ActivityResultLauncher<Intent>? = null
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var videoUtils: com.tqhit.battery.one.utils.VideoUtils
    @Inject lateinit var cacheDataSourceFactory: CacheDataSource.Factory

    override fun setupData() {
        super.setupData()
        // Flow: Check overlay permission, if not granted, show dialog and request permission
        // If granted, proceed with applying the animation
        overlayPermissionLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (!android.provider.Settings.canDrawOverlays(this)) {
                Toast.makeText(this, getString(R.string.overlay_permission_denied), Toast.LENGTH_SHORT).show()
                return@registerForActivityResult
            }

            // Hide banner when permission is granted
            hideOverlayPermissionBanner()
            updateButton()

            if (animationViewModel.isApplied(mediaUrl)) return@registerForActivityResult

            if (!animationViewModel.isExpired(mediaUrl)) {
                apply()
                updateButton()
            } else {
                animationViewModel.applyAnimation(mediaUrl)
                apply()
                updateButton()
            }
        }

        binding.dateTimeContainer.visibility = if (appRepository.isAnimationOverlayTimeEnabled())
            View.VISIBLE else View.GONE

        val rvLoadInView = remoteConfigHelper.getBoolean("rv_load_in_view")
        if (rvLoadInView) {
            applovinRewardedAdManager.loadRewardedAd()
        }

        // Proactive overlay permission check - start early while video is loading
        checkAndShowOverlayPermissionBanner()
    }

    override fun setupUI() {
        super.setupUI()
        val videoUrl = intent.getStringExtra("video_url") ?: run {
            Toast.makeText(this, getString(R.string.no_video_url_provided), Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        mediaUrl = videoUrl
        setupPlayer(videoUrl)
        // Update time/date immediately and start handler
        updateTimeAndDate()
        timeHandler.post(timeRunnable)
        // Register battery receiver
        registerReceiver(batteryReceiver, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
        updateButton()
        startTimer()
    }

    override fun setupListener() {
        super.setupListener()

        binding.backButton.setOnClickListener {
            setupInterstitialAd()
        }
        
        // Overlay permission banner action
        binding.overlayPermissionBanner.setOnClickListener {
            val intent = OverlayPermissionUtils.createOverlayPermissionIntent(this)
            if (overlayPermissionLauncher != null) {
                overlayPermissionLauncher!!.launch(intent)
            } else {
                BatteryLogger.e("AnimationActivity", "overlayPermissionLauncher is not initialized")
                Toast.makeText(this, getString(R.string.overlay_permission_error), Toast.LENGTH_SHORT).show()
            }
        }
        
        binding.applyButton.setOnClickListener {
            if (animationViewModel.isApplied(mediaUrl)) return@setOnClickListener

            applovinRewardedAdManager.showRewardedAd(
                this,
                "animation_trial"
            ) {
                // Check overlay permission using centralized utility
                if (!OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
                    OverlayPermissionUtils.showOverlayPermissionDialog(
                        context = this,
                        onConfirm = {
                            val intent = OverlayPermissionUtils.createOverlayPermissionIntent(this)
                            if (overlayPermissionLauncher != null) {
                                overlayPermissionLauncher!!.launch(intent)
                            } else {
                                BatteryLogger.e("AnimationActivity", "overlayPermissionLauncher is not initialized")
                                Toast.makeText(this, getString(R.string.overlay_permission_error), Toast.LENGTH_SHORT).show()
                            }
                        },
                        onCancel = {
                            BatteryLogger.d("AnimationActivity", "User cancelled overlay permission request")
                        }
                    )
                    return@showRewardedAd
                }

                if (!animationViewModel.isExpired(mediaUrl)) {
                    apply()
                    updateButton()
                } else {
                    animationViewModel.applyAnimation(mediaUrl)
                    apply()
                    updateButton()
                }
            }
        }
    }

    private fun setupInterstitialAd(){
        applovinInterstitialAdManager.showInterstitialAd(
            "default_iv",
            this,
            onNext = {
                finish()
            }
        )
    }



    private fun updateButton() {
        val isExpired = animationViewModel.isExpired(mediaUrl)
        val isApplied = animationViewModel.isApplied(mediaUrl)
        val hasOverlayPermission = OverlayPermissionUtils.isOverlayPermissionGranted(this)
        
        binding.applyButton.background = ContextCompat.getDrawable(this, R.drawable.colorr_button)
        
        // Update banner visibility based on permission state
        if (hasOverlayPermission) {
            hideOverlayPermissionBanner()
        } else {
            showOverlayPermissionBanner()
        }
        
        if (!isExpired) {
            binding.timeRemaining.visibility = View.VISIBLE
            binding.timeRemainingValue.visibility = View.VISIBLE
            binding.iconAd.visibility = View.GONE
            binding.textBtn.text = getString(R.string.apply)
            binding.textBtn.setTextColor(getThemeColor(R.attr.grey))
            if (isApplied) {
                binding.applyButton.background = ContextCompat.getDrawable(this, R.drawable.grey_button)
                binding.textBtn.setTextColor(getThemeColor(R.attr.black))
                binding.textBtn.text = getString(R.string.applied)
            }
        } else {
            binding.iconAd.visibility = View.VISIBLE
            binding.textBtn.text = getString(R.string.apply_for_24hrs)
            binding.timeRemaining.visibility = View.GONE
            binding.timeRemainingValue.visibility = View.GONE
        }
    }

    private fun startTimer() {
        lifecycleScope.launch {
            while (true) {
                binding.timeRemainingValue.text = DateTimeUtils.formatMillisToTimeString(animationViewModel.getTimeRemaining(mediaUrl))
                delay(1000L)
            }
        }
    }

    private fun apply() {
        // Check overlay permission using centralized utility
        if (!OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
            OverlayPermissionUtils.showOverlayPermissionDialog(
                context = this,
                onConfirm = {
                    val intent = OverlayPermissionUtils.createOverlayPermissionIntent(this)
                    if (overlayPermissionLauncher != null) {
                        overlayPermissionLauncher!!.launch(intent)
                    } else {
                        BatteryLogger.e("AnimationActivity", "overlayPermissionLauncher is not initialized in apply method")
                        Toast.makeText(this, getString(R.string.overlay_permission_error), Toast.LENGTH_SHORT).show()
                    }
                },
                onCancel = {
                    BatteryLogger.d("AnimationActivity", "User cancelled overlay permission request in apply method")
                }
            )
        } else {
            proceedAfterOverlayPermission()
        }
    }

    @OptIn(UnstableApi::class)
    private fun setupPlayer(videoUrl: String) {
        // Always show the progress dialog. It will be dismissed quickly if the video is cached.
        progressLoadingDialog = ProgressLoadingDialog(this, getString(R.string.loading_video))
        progressLoadingDialog?.show()

        val playerView = binding.playerView
        // ExoPlayer cache integration - use cached data source factory
        val exoPlayer = ExoPlayer.Builder(this)
            .setMediaSourceFactory(
                androidx.media3.exoplayer.source.DefaultMediaSourceFactory(cacheDataSourceFactory)
            )
            .build()
            .also { player = it }
            
        playerView.player = exoPlayer
        playerView.useController = false // Hide all controls
        playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
        val mediaItem = MediaItem.fromUri(videoUrl.toUri())
        exoPlayer.setMediaItem(mediaItem)
        exoPlayer.repeatMode = ExoPlayer.REPEAT_MODE_ONE
        exoPlayer.prepare()
        exoPlayer.playWhenReady = true
        
        // Enhanced ExoPlayer listener with progress tracking
        exoPlayer.addListener(object : androidx.media3.common.Player.Listener {
            override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                progressLoadingDialog?.dismiss()
                progressLoadingDialog = null
                progressMonitoringJob?.cancel()
                progressMonitoringJob = null
                
                Toast.makeText(this@AnimationActivity,
                    getString(R.string.cannot_load_video), Toast.LENGTH_SHORT).show()
                finish()
            }
            
            override fun onPlaybackStateChanged(playbackState: Int) {
                when (playbackState) {
                    androidx.media3.common.Player.STATE_READY -> {
                        // Video is ready to play
                        progressLoadingDialog?.updateProgress(100)
                        progressLoadingDialog?.updateMessage(getString(R.string.video_ready))
                        
                        // Dismiss dialog after brief delay
                        lifecycleScope.launch {
                            delay(500)
                            progressLoadingDialog?.dismiss()
                            progressLoadingDialog = null
                            progressMonitoringJob?.cancel()
                            progressMonitoringJob = null
                        }
                    }
                    androidx.media3.common.Player.STATE_BUFFERING -> {
                        // Start progress monitoring if not already running
                        if (progressMonitoringJob == null && progressLoadingDialog?.isShowing == true) {
                            startProgressMonitoring(exoPlayer)
                        }
                    }
                }
            }
            
            override fun onIsLoadingChanged(isLoading: Boolean) {
                if (isLoading && progressMonitoringJob == null && progressLoadingDialog?.isShowing == true) {
                    startProgressMonitoring(exoPlayer)
                }
            }
        })
    }
    
    private fun startProgressMonitoring(exoPlayer: ExoPlayer) {
        progressMonitoringJob = lifecycleScope.launch {
            while (progressLoadingDialog?.isShowing == true && exoPlayer.playbackState != androidx.media3.common.Player.STATE_READY) {
                val bufferedPercentage = exoPlayer.bufferedPercentage
                val progress = bufferedPercentage.coerceIn(0, 99) // Keep under 100% until ready
                
                progressLoadingDialog?.updateProgress(progress)
                
                if (bufferedPercentage > 0) {
                    progressLoadingDialog?.updateMessage(getString(R.string.buffering_video))
                }
                
                delay(100) // Update every 100ms for smooth progress
            }
        }
    }

    /**
     * Check overlay permission and show banner if needed (proactive approach)
     */
    private fun checkAndShowOverlayPermissionBanner() {
        if (!OverlayPermissionUtils.isOverlayPermissionGranted(this)) {
            showOverlayPermissionBanner()
        } else {
            hideOverlayPermissionBanner()
        }
    }

    /**
     * Show the overlay permission banner
     */
    private fun showOverlayPermissionBanner() {
        binding.overlayPermissionBanner.visibility = View.VISIBLE
        BatteryLogger.d("AnimationActivity", "Showing overlay permission banner")
    }

    /**
     * Hide the overlay permission banner
     */
    private fun hideOverlayPermissionBanner() {
        binding.overlayPermissionBanner.visibility = View.GONE
        BatteryLogger.d("AnimationActivity", "Hiding overlay permission banner")
    }

    private fun updateTimeAndDate() {
        binding.textTime.text = DateTimeUtils.getCurrentTimeString()
        binding.textDate.text = DateTimeUtils.getCurrentDateString()
    }

    private fun proceedAfterOverlayPermission() {
        val videoUrl = intent.getStringExtra("video_url")
        if (videoUrl == null) {
            if (!isFinishing && !isDestroyed) {
                Toast.makeText(this, getString(R.string.no_video_url_provided), Toast.LENGTH_SHORT).show()
            }
            return
        }

        // 1. Extract the dynamic filename from the URL
        val dynamicFileName = VideoUtils.getFileNameFromUrl(videoUrl)
        val destinationFile = File(filesDir, dynamicFileName)

        // --- INSTANT ACTION ---
        // 2. Save the FULL PATH with the DYNAMIC filename for permanent storage
        appRepository.setVideoPath(destinationFile.absolutePath)
        // Also save the URL for immediate playback fallback
        appRepository.setAppliedAnimationUrl(videoUrl)
        appRepository.setAnimationOverlayEnabled(true)

        // Provide instant UI feedback
        if (!isFinishing && !isDestroyed) {
            BatteryLogger.d("AnimationActivity", "Animation instantly applied: $videoUrl")
            Toast.makeText(this@AnimationActivity, getString(R.string.animation_applied_instantly), Toast.LENGTH_SHORT).show()

            // Update button state immediately
            animationViewModel.setTrialEndTime(mediaUrl)
            animationViewModel.setApplied(mediaUrl)

            // --- BACKGROUND TASK ---
            // 3. Launch a background coroutine to save the permanent copy.
            lifecycleScope.launch(Dispatchers.IO) {
                try {
                    // Pass the dynamic filename to the enhanced saving method
                    videoUtils.downloadAndSaveVideoEnhanced(videoUrl, dynamicFileName)
                    BatteryLogger.d("AnimationActivity", "Background video copy completed to: ${destinationFile.absolutePath}")
                } catch (e: Exception) {
                    BatteryLogger.e("AnimationActivity", "Background video copy failed: ${e.message}")
                    // Animation still works from URL, so don't show error to user
                }
            }

            // 4. Navigate to completion activity immediately.
            val intent = Intent(this@AnimationActivity, PostAnimationCompleteActivity::class.java)
            intent.putExtra("video_url", mediaUrl) // Still pass mediaUrl for PostAnimationCompleteActivity's preview
            startActivity(intent)
            finish()
        } else {
            finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        
        // Clean up progress monitoring
        progressMonitoringJob?.cancel()
        progressMonitoringJob = null
        
        // Clean up progress dialog
        progressLoadingDialog?.dismiss()
        progressLoadingDialog = null
        
        player?.release()
        player = null
        // Remove callbacks and unregister receiver
        timeHandler.removeCallbacks(timeRunnable)
        try { unregisterReceiver(batteryReceiver) } catch (_: Exception) {}
    }

    private fun getThemeColor(attr: Int): Int {
        val typedValue = android.util.TypedValue()
        val theme = this.theme
        theme.resolveAttribute(attr, typedValue, true)
        return typedValue.data
    }
}