package com.tqhit.battery.one.activity.animation.onCompleted

import android.content.Intent
import android.os.Bundle
import android.view.View
import com.tqhit.battery.one.databinding.ActivityPostAnimationCompleteBinding
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import dagger.hilt.android.AndroidEntryPoint
import android.util.Log
import com.tqhit.battery.one.R
import android.net.Uri
import android.widget.Toast
import androidx.activity.viewModels
import androidx.annotation.OptIn
import androidx.core.net.toUri
import androidx.fragment.app.viewModels
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.ui.AspectRatioFrameLayout
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.dialog.capacity.SetupPasswordDialog
import com.tqhit.battery.one.dialog.permission.BackgroundPermissionDialog
import com.tqhit.battery.one.dialog.utils.NotificationDialog
import com.tqhit.battery.one.features.navigation.AppNavigator
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider
import com.tqhit.battery.one.utils.BackgroundPermissionManager
import com.tqhit.battery.one.viewmodel.AppViewModel
import jakarta.inject.Inject
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

@AndroidEntryPoint
class PostAnimationCompleteActivity : AdLibBaseActivity<ActivityPostAnimationCompleteBinding>() {

    companion object {
        private const val TAG = "PostAnimationPermission"
    }

    @Inject
    lateinit var coreBatteryStatsProvider: CoreBatteryStatsProvider

    @Inject
    lateinit var appNavigator: AppNavigator
    
    @Inject
    lateinit var cacheDataSourceFactory: CacheDataSource.Factory
    
    @Inject
    lateinit var videoUtils: com.tqhit.battery.one.utils.VideoUtils

    override val binding by lazy { ActivityPostAnimationCompleteBinding.inflate(layoutInflater) }
    private var player: ExoPlayer? = null
    private val appViewModel: AppViewModel by viewModels()
    private var backgroundPermissionDialog: BackgroundPermissionDialog? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // visible and gone UI for review ( not useful )
//        startBatteryStatusMonitoring(this)


        handleBackButton()


    }

    private fun handleBackButton(){
        binding.includeBackNavigation.btnBackNavigation.visibility = View.GONE

        // back
        binding.includeBackNavigation.btnBackNavigation.postDelayed({
            binding.includeBackNavigation.btnBackNavigation.visibility = View.VISIBLE
        }, 2000)

        binding.includeBackNavigation.btnBackNavigation.setOnClickListener {
            finish()
        }

        // others
        binding.nextPage.setOnClickListener{
            navigateToOthersFragment()
        }
    }

    override fun setupUI() {
        super.setupUI()
        val videoUrl = intent.getStringExtra("video_url") ?: run {
            Toast.makeText(this, getString(R.string.no_video_url_provided), Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        setupPlayer(videoUrl)
        setupAntiThiefInfo()
        checkAndShowBackgroundPermissionDialogAfterAnimation()

    }

    private fun setupAntiThiefInfo() {
        // Set info icon click listener
        binding.switchAntiThiefBlock.setOnClickListener {
            showAntiTheftInfoDialog()
        }
    }
    @OptIn(UnstableApi::class)
    private fun setupPlayer(videoUrl: String) {
        // Smart loading - since this plays the same video that was just applied,
        // it should load instantly from cache
        lifecycleScope.launch {
            val isVideoAvailable = videoUtils.isVideoPreloaded(videoUrl)
            if (!isVideoAvailable) {
                // This shouldn't happen since video was just applied, but add minimal delay
                // to prevent any potential UI flash
                kotlinx.coroutines.delay(100)
            }
        }
        
        val playerView = binding.playerView
        // ExoPlayer cache integration - use cached data source factory
        val exoPlayer = ExoPlayer.Builder(this)
            .setMediaSourceFactory(
                androidx.media3.exoplayer.source.DefaultMediaSourceFactory(cacheDataSourceFactory)
            )
            .build()
            .also { player = it }
            
        playerView.player = exoPlayer
        playerView.useController = false // Hide all controls
        playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
        val mediaItem = MediaItem.fromUri(videoUrl.toUri())
        exoPlayer.setMediaItem(mediaItem)
        exoPlayer.repeatMode = ExoPlayer.REPEAT_MODE_ONE
        exoPlayer.prepare()
        exoPlayer.playWhenReady = true
        // Listen for error
        exoPlayer.addListener(object : androidx.media3.common.Player.Listener {
            override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                Toast.makeText(this@PostAnimationCompleteActivity,
                    getString(R.string.cannot_load_video), Toast.LENGTH_SHORT).show()
                finish()
            }
        })
    }

    /**
     * Starts monitoring battery status changes and updates navigation accordingly.
     */
    private fun startBatteryStatusMonitoring(lifecycleOwner: LifecycleOwner) {
        lifecycleOwner.lifecycleScope.launch {
            coreBatteryStatsProvider.coreBatteryStatusFlow
                .filterNotNull()
                .map { it.isCharging }
                .distinctUntilChanged()
                .collect { isCharging ->

                    changeUI(!isCharging)
                }
        }
    }

    private fun changeUI(isCharging: Boolean){
        binding.titleText.fadeVisibility(isCharging)

        binding.includeBackNavigation.backNavigationContainer.fadeVisibility(isCharging)
        binding.titleText.fadeVisibility(isCharging)

        binding.lottieView.fadeVisibility(isCharging)
        binding.descriptionText.fadeVisibility(isCharging)
        binding.switchAntiThiefBlock.fadeVisibility(isCharging)
        binding.nextPage.fadeVisibility(isCharging)
    }
    private fun View.fadeVisibility(visible: Boolean) {
        animate().alpha(if (visible) 1f else 0f).setDuration(300).withEndAction {
            visibility = if (visible) View.VISIBLE else View.GONE
        }.start()
    }


    /**
     * Shows information dialog about the anti-theft feature.
     */
    private fun showAntiTheftInfoDialog() {
        val isChecked = appViewModel.isAntiThiefEnabled()

        val dialog = NotificationDialog(
            this@PostAnimationCompleteActivity,
            getString(R.string.anti_thief),
            getString(if (!isChecked) R.string.anti_thief_info_active else R.string.anti_thief_info_inactive),
            if (!isChecked) ({ handleAntiTheftActive() }) else ({})

        )
        dialog.show()

    }

    /**
     * Handles anti-theft toggle state changes.
     * Shows password dialog if needed or updates preference directly.
     */
    private fun handleAntiTheftActive() {
        if (!appViewModel.isAntiThiefPasswordSet()) {
            showPasswordSetupDialog()
        } else {
            appViewModel.setAntiThiefEnabled(true)
        }
    }


    /**
     * Shows the password setup dialog for anti-theft feature.
     */
    private fun showPasswordSetupDialog() {
        try {
            val dialog = SetupPasswordDialog(
                context = this@PostAnimationCompleteActivity,
                onConfirm = { password ->
                    appViewModel.setAntiThiefPassword(password)
                    appViewModel.setAntiThiefEnabled(true)
                },
                onCancel = {
                }
            )
            dialog.show()
        } catch (e: Exception) {
        }
    }

    /**
     * FRAGMENT_LIFECYCLE_FIX: Navigate to Others fragment using centralized AppNavigator
     * This preserves fragment lifecycle and prevents ViewModel destruction during navigation.
     * Called by DischargeFragment's back navigation to maintain data consistency.
     */
    fun navigateToOthersFragment() {
        initializeAppNavigatorIfNeeded()

        try {
            // Try AppNavigator first (modern centralized approach)
            val navigationSuccess = appNavigator.navigateToOthers()
            if (navigationSuccess) {
            } else {
                navigateToOthersFragmentLegacy()
            }

        } catch (e: Exception) {
            navigateToOthersFragmentLegacy()
        }
    }


    /**
     * Initializes AppNavigator if not already initialized.
     * This ensures centralized navigation is available for back navigation.
     */
    private fun initializeAppNavigatorIfNeeded() {
        if (!appNavigator.isInitialized()) {
            try {
                val activity = this@PostAnimationCompleteActivity
                val fragmentManager = activity.supportFragmentManager
                val bottomNavigationView = activity.findViewById<com.google.android.material.bottomnavigation.BottomNavigationView>(R.id.bottom_view)
                val fragmentContainerId = R.id.nav_host_fragment

                if (bottomNavigationView != null) {
                    appNavigator.initialize(
                        fragmentManager = fragmentManager,
                        bottomNavigationView = bottomNavigationView,
                        fragmentContainerId = fragmentContainerId,
                        lifecycleOwner = this
                    )
                } else {
                }
            } catch (e: Exception) {
            }
        }
    }


    /**
     * Legacy navigation to Others fragment using DynamicNavigationManager.
     * Used as fallback when AppNavigator fails.
     */
    private fun navigateToOthersFragmentLegacy() {
            val intent = Intent(this, MainActivity::class.java).apply {
                putExtra("navigate_to", "others")
                flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            }
            startActivity(intent)
            finish()

    }

    /**
     * Checks and shows the background permission dialog after successful animation application.
     * Uses shouldShowBackgroundPermissionDialogIgnoreCooldown to show immediately after animation.
     */
    private fun checkAndShowBackgroundPermissionDialogAfterAnimation() {
        Log.d(TAG, "Checking if background permission dialog should be shown after animation")

        // Don't show dialog if activity is not in appropriate state
        if (isFinishing || isDestroyed) {
            Log.d(TAG, "Activity finishing or destroyed, skipping dialog")
            return
        }

        // Don't show dialog if one is already showing
        if (backgroundPermissionDialog?.isShowing == true) {
            Log.d(TAG, "Dialog already showing, skipping")
            return
        }

        // Check if permission dialog should be shown (ignores cooldown for post-animation context)
        if (BackgroundPermissionManager.shouldShowBackgroundPermissionDialogIgnoreCooldown(this)) {
            Log.d(TAG, "Showing background permission dialog after animation")
            showBackgroundPermissionDialogAfterAnimation()
        } else {
            Log.d(TAG, "Background permission dialog not needed after animation")
        }
    }

    /**
     * Shows the background permission dialog in the post-animation context.
     */
    private fun showBackgroundPermissionDialogAfterAnimation() {
        try {
            backgroundPermissionDialog = BackgroundPermissionDialog(
                context = this,
                onPermissionGranted = {
                    Log.d(TAG, "Permission granted via dialog after animation")
                    // Permission granted - no additional action needed
                },
                onPermissionDenied = {
                    Log.d(TAG, "Permission denied via dialog after animation")
                    // Permission denied - no additional action needed
                },
                onDialogClosed = {
                    Log.d(TAG, "Background permission dialog closed after animation")
                    backgroundPermissionDialog = null
                }
            )
            backgroundPermissionDialog?.show()
            Log.d(TAG, "Background permission dialog shown successfully after animation")
        } catch (e: Exception) {
            Log.e(TAG, "Error showing background permission dialog after animation", e)
            backgroundPermissionDialog = null
        }
    }



    override fun onDestroy() {
        super.onDestroy()
        // Clean up background permission dialog
        backgroundPermissionDialog?.dismiss()
        backgroundPermissionDialog = null
        
        player?.release()
        player = null
    }

}


