package com.tqhit.battery.one.utils

import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.DataSpec
import com.tqhit.battery.one.service.ChargingOverlayService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Utility class for ExoPlayer cache operations.
 * Provides methods to check cache status, extract cached videos, and manage cache.
 */
@Singleton
@UnstableApi
class ExoPlayerCacheUtils @Inject constructor(
    private val cache: SimpleCache
) {
    companion object {
        private const val TAG = "ExoPlayerCacheUtils"
    }

    /**
     * Check if a video URL is completely cached.
     * 
     * @param url Video URL to check
     * @return true if video is fully cached, false otherwise
     */
    suspend fun isFullyCached(url: String): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            val dataSpec = DataSpec.Builder()
                .setUri(url)
                .build()
            
            // Check if cache contains any data for this URL
            val keys = cache.keys
            val hasKey = keys.any { key -> key.contains(url.hashCode().toString()) || key.contains(url) }
            
            BatteryLogger.d(TAG, "Cache check for $url: has cached data: $hasKey")
            hasKey
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error checking cache status for $url", e)
            false
        }
    }

    /**
     * Extract cached video to internal storage file for overlay service.
     * 
     * @param url Video URL to extract from cache
     * @param destFile Destination file to copy cached video to
     * @return true if successfully copied from cache, false if cache incomplete
     */
    suspend fun copyFromCacheToFile(url: String, destFile: File): Boolean = withContext(Dispatchers.IO) {
        return@withContext try {
            if (!isFullyCached(url)) {
                BatteryLogger.d(TAG, "Video not fully cached, cannot copy: $url")
                return@withContext false
            }

            val dataSpec = DataSpec.Builder()
                .setUri(url)
                .build()

            // Create a temporary file to read from cache
            destFile.parentFile?.mkdirs()
            
            // For now, we'll mark this as a simplified implementation
            // In a production environment, you would use CacheDataSource.Factory
            // to read from cache and write to the destination file
            // This is a limitation of the current ExoPlayer cache API that requires
            // more complex implementation for direct file extraction
            
            BatteryLogger.w(TAG, "Cache-to-file copy not fully implemented, falling back to network/preload")
            false
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error in copyFromCacheToFile for $url", e)
            false
        }
    }

    /**
     * Get current cache usage in bytes.
     * 
     * @return Current cache size in bytes
     */
    suspend fun getCacheSize(): Long = withContext(Dispatchers.IO) {
        return@withContext try {
            val cacheSize = cache.cacheSpace
            BatteryLogger.d(TAG, "Current cache size: ${cacheSize / (1024 * 1024)} MB")
            cacheSize
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting cache size", e)
            0L
        }
    }

    /**
     * Clear the entire cache.
     * Use with caution as this will remove all cached content.
     */
    suspend fun clearCache(): Unit = withContext(Dispatchers.IO) {
        try {
            BatteryLogger.d(TAG, "Clearing ExoPlayer cache")
            // Clear cache by removing all keys
            val keysToRemove = cache.keys.toList()
            for (key in keysToRemove) {
                cache.removeResource(key)
            }
            BatteryLogger.d(TAG, "Cache cleared successfully")
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error clearing cache", e)
        }
    }

    /**
     * Get cache keys for debugging purposes.
     * 
     * @return Set of cache keys
     */
    suspend fun getCacheKeys(): Set<String> = withContext(Dispatchers.IO) {
        return@withContext try {
            val keys = cache.keys.toSet()
            BatteryLogger.d(TAG, "Cache contains ${keys.size} keys")
            keys
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error getting cache keys", e)
            emptySet()
        }
    }
}
