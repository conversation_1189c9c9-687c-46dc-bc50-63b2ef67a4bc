package com.tqhit.battery.one.activity.overlay

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.BatteryManager
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.annotation.OptIn
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.utils.DateTimeUtils
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.ui.AspectRatioFrameLayout
import com.tqhit.battery.one.R
import com.tqhit.battery.one.databinding.ActivityChargingOverlayBinding
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.AndroidEntryPoint
import jakarta.inject.Inject

@AndroidEntryPoint
class ChargingOverlayActivity : AdLibBaseActivity<ActivityChargingOverlayBinding>() {
    override val binding by lazy { ActivityChargingOverlayBinding.inflate(layoutInflater) }
    @Inject lateinit var appRepository: AppRepository
    @Inject lateinit var cacheDataSourceFactory: CacheDataSource.Factory
    private var player: ExoPlayer? = null
    private var videoPath: String? = null
    private val timeHandler = Handler(Looper.getMainLooper())
    private val timeRunnable = object : Runnable {
        override fun run() {
            updateTimeAndDate()
            timeHandler.postDelayed(this, 60_000L)
        }
    }
    private val batteryReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val level = intent?.getIntExtra(BatteryManager.EXTRA_LEVEL, -1) ?: -1
            if (level >= 0) {
                binding.batteryPercent.text = "$level%"
            }
        }
    }

    override fun setupData() {
        super.setupData()

        binding.dateTimeContainer.visibility = if (appRepository.isAnimationOverlayTimeEnabled())
            View.VISIBLE else View.GONE
    }

    override fun setupUI() {
        super.setupUI()
        // Window flags for lock screen overlay
        window.addFlags(
            WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_FULLSCREEN or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
        )
        // Determine video source using priority logic
        videoPath = determineVideoSource()
        if (videoPath.isNullOrEmpty()) {
            finish()
            return
        }
        // Update time/date immediately and start handler
        updateTimeAndDate()
        scheduleNextTimeUpdate()
        // Register battery receiver
        registerReceiver(batteryReceiver, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
    }
    
    private fun determineVideoSource(): String? {
        // First priority: Check if permanent file exists
        val permanentFilePath = appRepository.getVideoPath()
        if (!permanentFilePath.isNullOrEmpty()) {
            val file = java.io.File(permanentFilePath)
            if (file.exists() && file.length() > 0) {
                return permanentFilePath
            }
        }
        
        // Second priority: Fall back to applied animation URL for immediate playback
        val appliedAnimationUrl = appRepository.getAppliedAnimationUrl()
        if (!appliedAnimationUrl.isNullOrEmpty()) {
            return appliedAnimationUrl
        }
        
        // Third priority: Legacy video path from intent
        return intent.getStringExtra("extra_video_path")
    }

    override fun setupListener() {
        super.setupListener()

        binding.root.setOnClickListener {
            finish()
        }
    }

    @OptIn(UnstableApi::class)
    private fun setupPlayer() {
        val path = videoPath ?: return
        val playerView = binding.playerView
        
        // ExoPlayer cache integration - use cache as fallback but prioritize local file
        val exoPlayer = ExoPlayer.Builder(this)
            .setMediaSourceFactory(
                androidx.media3.exoplayer.source.DefaultMediaSourceFactory(cacheDataSourceFactory)
            )
            .build()
            .also { player = it }
            
        playerView.player = exoPlayer
        playerView.useController = false // Hide all controls
        playerView.resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
        
        // Priority playback logic: try local file first, then URL
        val mediaItem = if (java.io.File(path).exists()) {
            // Local file exists - use it for maximum reliability
            MediaItem.fromUri(Uri.fromFile(java.io.File(path)))
        } else {
            // Fall back to URL with ExoPlayer cache
            MediaItem.fromUri(path)
        }
        
        exoPlayer.setMediaItem(mediaItem)
        exoPlayer.repeatMode = ExoPlayer.REPEAT_MODE_ONE
        exoPlayer.prepare()
        exoPlayer.playWhenReady = true
        exoPlayer.addListener(object : androidx.media3.common.Player.Listener {
            override fun onPlayerError(error: androidx.media3.common.PlaybackException) {
                // If local file failed and we have a URL, try the URL
                val appliedUrl = appRepository.getAppliedAnimationUrl()
                if (java.io.File(path).exists() && !appliedUrl.isNullOrEmpty() && appliedUrl != path) {
                    // Try fallback to URL
                    val fallbackMediaItem = MediaItem.fromUri(appliedUrl)
                    exoPlayer.setMediaItem(fallbackMediaItem)
                    exoPlayer.prepare()
                    exoPlayer.playWhenReady = true
                } else {
                    Toast.makeText(this@ChargingOverlayActivity, getString(R.string.cannot_load_video), Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
        })
    }

    private fun updateTimeAndDate() {
        binding.textTime.text = DateTimeUtils.getCurrentTimeString()
        binding.textDate.text = DateTimeUtils.getCurrentDateString()
    }

    private fun scheduleNextTimeUpdate() {
        timeHandler.removeCallbacks(timeRunnable)
        val now = java.util.Calendar.getInstance()
        val seconds = now.get(java.util.Calendar.SECOND)
        val millis = now.get(java.util.Calendar.MILLISECOND)
        val delay = (60 - seconds) * 1000 - millis
        timeHandler.postDelayed({
            updateTimeAndDate()
            timeHandler.postDelayed(timeRunnable, 60_000L)
        }, delay.toLong())
    }

    override fun onStart() {
        super.onStart()
        if (player == null && !videoPath.isNullOrEmpty()) {
            setupPlayer()
        } else if (player != null && !videoPath.isNullOrEmpty()) {
            // If player exists but videoPath changed, update media item using priority logic
            val mediaItem = if (java.io.File(videoPath!!).exists()) {
                MediaItem.fromUri(Uri.fromFile(java.io.File(videoPath!!)))
            } else {
                MediaItem.fromUri(videoPath!!)
            }
            player?.setMediaItem(mediaItem)
            player?.prepare()
            player?.playWhenReady = true
        }
        binding.playerView.player = player
    }

    override fun onStop() {
        super.onStop()
        player?.release()
        player = null
        binding.playerView.player = null
    }

    override fun onDestroy() {
        super.onDestroy()
        // player released in onStop
        timeHandler.removeCallbacks(timeRunnable)
        try { unregisterReceiver(batteryReceiver) } catch (_: Exception) {}
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        val newVideoPath = determineVideoSource()
        if (!newVideoPath.isNullOrEmpty() && newVideoPath != videoPath) {
            videoPath = newVideoPath
            if (player != null) {
                val mediaItem = if (java.io.File(videoPath!!).exists()) {
                    MediaItem.fromUri(Uri.fromFile(java.io.File(videoPath!!)))
                } else {
                    MediaItem.fromUri(videoPath!!)
                }
                player?.setMediaItem(mediaItem)
                player?.prepare()
                player?.playWhenReady = true
            }
        }
    }
} 