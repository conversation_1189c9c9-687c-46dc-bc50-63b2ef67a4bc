## Codebase Summary: TJ_BatteryOne

### 1. Overall Purpose

The application is a comprehensive battery utility designed to provide users with detailed statistics about their device's battery charging and discharging cycles, health information, and various customization options. It features distinct, modern modules for tracking both charging and discharging behavior with greater accuracy. Aesthetic enhancements like charging animations, security features like anti-theft protection, and configurable battery alarms are also key components. The app aims to empower users with a better understanding and control over their device's battery performance and longevity.

### 2. Key Features

*   **Battery Statistics (Charging & Discharging):**
    *   Real-time monitoring of battery status (percentage, charging state, current, voltage, temperature).
    *   Manages charge and discharge sessions, tracking start/end times, percentages, estimated mAh charged/discharged, screen on/off times, and average charge/discharge rates.
    *   Calculates estimated time to full charge and time remaining for discharge.
    *   Provides UI for displaying this data.
    *   Includes services for background monitoring and notifications.
*   **Battery Health:**
    *   Calculated battery health percentage.
    *   Estimated battery wear in cycles.
    *   Display of design vs. user-configurable battery capacity.
    *   Daily battery wear tracking and visualization.
*   **Charging Animations:**
    *   Selection and application of video animations during charging.
    *   Animations sourced from Firebase Remote Config.
    *   `ExoPlayer` for video playback.
    *   `ChargingOverlayService` for lock screen overlay.
*   **Customization:**
    *   Theme selection (Light, Dark, Amoled, Grey, Auto, and inverted versions).
    *   Accent color selection.
    *   Language selection, dynamically updating UI.
*   **Alarms & Notifications:**
    *   Charge Alarms (target percentage, full charge, charging started/stopped).
    *   Discharge Alarms (low battery).
    *   Do Not Disturb mode for alarms.
    *   Persistent notification displaying key battery metrics.
*   **Anti-Theft Protection:**
    *   Alerts if charger is disconnected while active.
    *   Requires user-set password to disable.
*   **Onboarding & Permissions:**
    *   Guides new users through privacy policy, notification permission, and battery optimization.
*   **Monetization:**
    *   AppLovin Ad SDK integration (Banner, Interstitial, Rewarded, App Open, Native).
    *   Firebase Remote Config for ad behavior and feature flags.

### 3. Project Structure

The project is organized into a main application package `com.tqhit.battery.one` with sub-packages for activities, fragments, services, repositories, viewmodels, dialogs, and feature modules.

```
├── java/com/tqhit/battery/one/
│   ├── activity/
│   ├── ads/core/
│   ├── component/
│   ├── dialog/
│   ├── features/
│   ├── fragment/
│   ├── initialization/
│   ├── manager/
│   ├── model/
│   ├── repository/
│   ├── service/
│   ├── utils/
│   ├── viewmodel/
│   └── BatteryApplication.kt
├── res/
...
```

### 4. File Breakdown and Summaries

#### 4.1. Core Application & Setup

*   **`BatteryApplication.kt`**: Initializes Firebase, AppLovin SDK, `ThemeManager`, language. Manages app session count.
*   **`AndroidManifest.xml`**: Declares components, permissions. Launcher is `SplashActivity`.

#### 4.2. Activities

*   **`SplashActivity.kt`**: Navigates to `StartingActivity` or `MainActivity`.
*   **`StartingActivity.kt`**: Onboarding for privacy, permissions, initial alarm setup.
*   **`MainActivity.kt`**: Main UI host with `BottomNavigationView`.
*   **`AnimationActivity.kt`**: Displays charging animation with `ExoPlayer`.
*   **`ChargingOverlayActivity.kt`**: Lock screen overlay for animations.
*   **`EnterPasswordActivity.kt`**: Password prompt for anti-theft.

#### 4.3. Ad Management (`ads/core/`)

*   **`ApplovinAppOpenAdManager.kt`**: Manages App Open ads.
*   **`ApplovinBannerAdManager.kt`**: Manages Banner ads.
*   **`ApplovinInterstitialAdManager.kt`**: Manages Interstitial ads.
*   **`ApplovinNativeAdManager.kt`**: Manages Native ads.
*   **`ApplovinRewardedAdManager.kt`**: Manages Rewarded ads.

### 5. App Startup & Ad Logic

#### 5.1. App Startup Flow

1.  **`BatteryApplication.onCreate()`**:
    *   Initializes Firebase, AppLovin SDK, `ThemeManager`, and language settings.
    *   Increments the app session counter.
    *   Initializes ad managers.
    *   Starts background services for battery monitoring.
2.  **`SplashActivity.onCreate()`**:
    *   Displays a splash screen.
    *   Checks if the user has completed the initial onboarding.
    *   If onboarding is complete, it navigates to `MainActivity`.
    *   If not, it navigates to `StartingActivity`.
    *   Shows an interstitial ad if ready.
3.  **`StartingActivity` (First-time users)**:
    *   Presents a multi-page `ViewPager` for:
        *   Privacy Policy acceptance.
        *   Notification permission request.
        *   Battery optimization permission request.
        *   Initial charge alarm setup.
    *   Once completed, it navigates to `MainActivity`, showing an interstitial ad if ready.
4.  **`MainActivity.onCreate()`**:
    *   Sets up the `BottomNavigationView`.
    *   Loads the initial fragment.
    *   Initializes and loads a banner ad.
    *   Shows an interstitial ad if ready.

#### 5.2. Ad Loading and Display Logic

*   **Ad Initialization**:
    *   The AppLovin SDK is initialized in `BatteryApplication.onCreate()`.
    *   Ad unit IDs are hardcoded in the respective `Applovin...AdManager` classes.
    *   Firebase Remote Config is used to enable/disable ad types and control behavior (e.g., delays, frequency caps).

*   **Ad Loading**:
    *   **App Open Ads (`ApplovinAppOpenAdManager`)**: Loaded when `loadAppOpenAd()` is called. An ad is loaded in `BatteryApplication` and shown when the app is foregrounded.
    *   **Banner Ads (`ApplovinBannerAdManager`)**: Loaded in `MainActivity.initBannerAd()` and placed in the `bannerContainer`.
    *   **Interstitial Ads (`ApplovinInterstitialAdManager`)**: Pre-loaded when `loadInterstitialAd()` is called. This is done in `BatteryApplication`.
    *   **Rewarded Ads (`ApplovinRewardedAdManager`)**: Pre-loaded via `loadRewardedAd()`. This is done in `BatteryApplication`.
    *   **Native Ads (`ApplovinNativeAdManager`)**: Loaded via `loadNativeAd()` in `StartingViewAdapter`.

*   **Ad Display Logic**:
    *   **App Open Ads**: Shown via `showAppOpenAd()` when the app is brought to the foreground (`onAppForegrounded` in `BatteryApplication`).
    *   **Banner Ads**: Displayed at the bottom of `MainActivity` if enabled in Remote Config.
    *   **Interstitial Ads**: Shown via `showInterstitialAd()`. This is called in `SplashActivity` and `StartingActivity` before navigating to the next screen, and in `MainActivity` when navigating between fragments. Display is subject to frequency and timing rules from Remote Config.
    *   **Rewarded Ads**: Shown on-demand via `showRewardedAd()`, typically triggered by a user action.
    *   **Native Ads**: Integrated into the onboarding flow in `StartingActivity`.

### 6. Asset Files

*   **`app/src/main/assets`**: Contains thumbnail images for animations.
*   **`app/src/main/res`**: Contains all standard Android resources, including:
    *   `drawable`: Icons, backgrounds, and other images.
    *   `layout`: XML files defining the UI for activities and fragments.
    *   `values`: Strings, colors, styles, and other resource values.
    *   `anim`, `animator`: Animation resources.
    *   `font`: Font files.
    *   `menu`: Menu definitions.
    *   `mipmap`: Launcher icons.
    *   `navigation`: Navigation graphs.
    *   `raw`: Raw resource files.
    *   `xml`: XML configuration files.
