## Animation Charging Feature Flow (v2, Optimized)

This document outlines the optimized flow of the Charging Animation feature. The latest version focuses on providing an instantaneous user experience by decoupling UI feedback from background tasks, improving visual consistency, and ensuring reliable video playback through a multi-layered caching and playback strategy.

### 1. Core Components

*   **`AnimationActivity.kt`:** The central screen for previewing and applying animations. It now provides instant UI feedback, proactive permission requests, and initiates background processes.
*   **`ChargingOverlayActivity.kt`:** Displays the final animation over the lock screen. It now features a priority-based playback system, first checking for a local file and falling back to a cached URL.
*   **`AppRepository.kt`:** Now stores both the permanent file path (`videoPath`) and the currently applied animation URL (`appliedAnimationUrl`) to enable the new playback strategy.
*   **`ProgressLoadingDialog.kt`:** A dialog that reliably shows video buffering progress. The logic has been fixed to ensure it displays correctly every time a video is loaded from the network.
*   **`ExoPlayerCacheModule.kt` & `VideoUtils.kt`:** These components manage a 150MB video cache and a sophisticated, multi-step process for saving the video file, prioritizing cache, preloaded files, and finally network downloads.

### 2. App Flow Diagram (Optimized v2)

```mermaid
graph TD
    A[Gallery] -->|Select Animation| B(AnimationActivity);
    B --> B1{Video in Cache?};
    B1 -->|No| B2[Show Progress Dialog & Buffer Video];
    B1 -->|Yes| B3[Show Animation Preview Instantly];
    B2 --> B3;
    B3 --> C{Permission Granted?};
    C -->|No| D[Show Themed Permission Banner];
    D --> E[User Clicks Banner -> Request Permission];
    C -->|Yes| F[Apply Button Enabled];
    F --> G[User Clicks Apply -> Instant UI Feedback];
    G --> H(Launch Background Copy: Cache to File);
    G --> I(Navigate to Post-Apply Screen);
    subgraph "Device is Charging"
        J[ChargingOverlayService] --> K{Permanent File Exists?};
        K -->|Yes| L[Play from Local File];
        K -->|No| M[Play from Cached URL];
    end
```

### 3. Detailed Step-by-Step Flow

1.  **Gallery & Animation Selection:** Unchanged. The user selects an animation, which launches `AnimationActivity`.

2.  **Animation Preview & Buffering (`AnimationActivity`):**
    *   `AnimationActivity` checks if the video is in the ExoPlayer cache. If not, the `ProgressLoadingDialog` is **always shown** to correctly display buffering progress.
    *   If the video is fully cached, the preview starts instantly, and the progress dialog is dismissed very quickly, preventing a jarring UI flash.

3.  **Proactive & Themed Permission Handling:**
    *   `AnimationActivity` immediately checks for the overlay permission.
    *   If permission is missing, a **theme-aware banner** is displayed. The banner's background and text colors adapt to the app's light/dark theme for better visual integration.
    *   The icon on the banner now has a **black border** for improved visibility.
    *   The entire banner is clickable, simplifying the UI and making the permission request more accessible.

4.  **Instant Apply & Background Copy:**
    *   When the user clicks "Apply," the UI provides **instantaneous feedback**.
    *   The app immediately saves the **video URL** to SharedPreferences (`appliedAnimationUrl`) and updates the button to an "Applied" state.
    *   A **background coroutine** is launched simultaneously to handle the file I/O. It uses the `VideoUtils` to create a permanent copy of the video in the app's internal storage (`charging_animation.mp4`).
    *   The user is not blocked and can navigate away while the copy happens in the background.

5.  **Priority-Based Playback (`ChargingOverlayActivity`):**
    *   When the device is charging, `ChargingOverlayActivity` is launched.
    *   It first checks if the permanent `charging_animation.mp4` file exists.
    *   **If the file exists,** it plays from the local file. This is the most reliable method and works offline.
    *   **If the file does not exist** (because the background copy is still in progress or failed), it falls back to playing the animation from the **saved URL**, which will be served by the ExoPlayer cache.

### 4. Asset & UI Summary

*   **Thumbnails (Gallery):** Static images or GIFs from the local `assets` folder.
*   **Animation Preview (`AnimationActivity`):** A video streamed from a URL and cached by ExoPlayer.
*   **Charging Animation (`ChargingOverlayActivity`):** A video file (`.mp4`) played with priority: first from a permanent local file, with a fallback to the cached URL.
*   **Permission Banner:** Now fully themed with a `?attr/white` background, `#000000` (black) text, and a bordered icon for clarity.