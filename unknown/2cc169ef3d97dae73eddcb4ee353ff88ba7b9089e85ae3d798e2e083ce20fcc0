package com.tqhit.battery.one.repository

import android.content.Context
import android.content.res.Configuration
import com.tqhit.adlib.sdk.data.local.PreferencesHelper
import com.tqhit.battery.one.utils.BatteryUtils
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.Locale
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update

@Singleton
class AppRepository @Inject constructor(
    private val preferencesHelper: PreferencesHelper,
    @ApplicationContext private val context: Context
) {
    companion object {
        private const val KEY_CONSENT_FLOW_USER_GEOGRAPHY = "consent_flow_user_geography"
        private const val KEY_SHOWED_START_PAGE = "showed_start_page"
        private const val KEY_PRIVACY_POLICY_ACCEPTED = "privacy_policy_accepted"
        private const val KEY_CHARGE_ALARM_ENABLED = "charge_alarm_enabled"
        private const val KEY_DISCHARGE_ALARM_ENABLED = "discharge_alarm_enabled"
        private const val KEY_NOTIFY_FULL_CHARGE_ENABLED = "notify_full_charge_enabled"
        private const val KEY_VIBRATION_ENABLED = "vibration_enabled"
        private const val KEY_VIBRATION_CHARGE_ENABLED = "vibration_charge_enabled"
        private const val KEY_VIBRATION_DISCHARGE_ENABLED = "vibration_discharge_enabled"
        private const val KEY_DONT_DISTURB_CHARGE_ENABLED = "dont_disturb_charge_enabled"
        private const val KEY_DONT_DISTURB_DISCHARGE_ENABLED = "dont_disturb_discharge_enabled"
        private const val KEY_CHARGE_ALARM_PERCENT = "charge_alarm_percent"
        private const val KEY_DISCHARGE_ALARM_PERCENT = "discharge_alarm_percent"
        private const val KEY_DONT_DISTURB_CHARGE_FROM_TIME = "dont_disturb_charge_from_time"
        private const val KEY_DONT_DISTURB_CHARGE_UNTIL_TIME = "dont_disturb_charge_until_time"
        private const val KEY_DONT_DISTURB_DISCHARGE_FROM_TIME = "dont_disturb_discharge_from_time"
        private const val KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME = "dont_disturb_discharge_until_time"
        private const val KEY_CHARGE_NOTIFICATION_ENABLED = "charge_notification_enabled"
        private const val KEY_DISCHARGE_NOTIFICATION_ENABLED = "discharge_notification_enabled"
        private const val KEY_LANGUAGE = "language"
        private const val KEY_ANIMATION_OVERLAY_ENABLED = "animation_overlay_enabled"
        private const val KEY_ANIMATION_OVERLAY_TIME_ENABLED = "animation_overlay_time_enabled"
        private const val KEY_VIDEO_PATH = "video_path"
        private const val KEY_ANTI_THIEF_ENABLED = "anti_thief_enabled"
        private const val KEY_ANTI_THIEF_SOUND_ENABLED = "anti_thief_sound_enabled"
        private const val KEY_ANTI_THIEF_PASSWORD = "anti_thief_password"
        private const val KEY_ANTI_THIEF_ALERT_ACTIVE = "anti_thief_alert_active"
        private const val KEY_BATTERY_CAPACITY = "battery_capacity"
        private const val KEY_APPLIED_ANIMATION_URL = "applied_animation_url"
    }

    // StateFlow for charge alarm percentage
    private val _chargeAlarmPercentFlow = MutableStateFlow(getChargeAlarmPercent())
    val chargeAlarmPercentFlow: Flow<Int> = _chargeAlarmPercentFlow.asStateFlow()

    // For the new discharge feature
    fun getBatteryCapacity(): Int {
        val savedCapacity = preferencesHelper.getInt(KEY_BATTERY_CAPACITY, 0)
        if (savedCapacity > 0) {
            return savedCapacity
        }
        return BatteryUtils.getBatteryCapacity(context)
    }

    fun isConsentFlowUserGeography(): Boolean = preferencesHelper.getBoolean(KEY_CONSENT_FLOW_USER_GEOGRAPHY, false)
    fun setConsentFlowUserGeography(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_CONSENT_FLOW_USER_GEOGRAPHY, enabled)

    // Start Page Methods
    fun isShowedStartPage(): Boolean = preferencesHelper.getBoolean(KEY_SHOWED_START_PAGE, false)

    fun setShowedStartPage(showed: Boolean) = preferencesHelper.saveBoolean(KEY_SHOWED_START_PAGE, showed)

    // Privacy Policy Methods
    fun isPrivacyPolicyAccepted(): Boolean = preferencesHelper.getBoolean(KEY_PRIVACY_POLICY_ACCEPTED, false)

    fun acceptPrivacyPolicy() = preferencesHelper.saveBoolean(KEY_PRIVACY_POLICY_ACCEPTED, true)

    fun getPrivacyPolicyUrl(): String = "https://ahugames.com/privacy-policy.html"

    fun getDoNotKillMyAppUrl(): String = "https://dontkillmyapp.com/"

    // Battery Alarm Settings Methods
    fun isChargeAlarmEnabled(): Boolean = preferencesHelper.getBoolean(KEY_CHARGE_ALARM_ENABLED, false)

    fun setChargeAlarmEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_CHARGE_ALARM_ENABLED, enabled)

    fun isDischargeAlarmEnabled(): Boolean = preferencesHelper.getBoolean(KEY_DISCHARGE_ALARM_ENABLED, false)

    fun setDischargeAlarmEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_DISCHARGE_ALARM_ENABLED, enabled)

    fun isNotifyFullChargeEnabled(): Boolean = preferencesHelper.getBoolean(KEY_NOTIFY_FULL_CHARGE_ENABLED, false)

    fun setNotifyFullChargeEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_NOTIFY_FULL_CHARGE_ENABLED, enabled)

    fun isVibrationEnabled(): Boolean = preferencesHelper.getBoolean(KEY_VIBRATION_ENABLED, true)

    fun setVibrationEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_VIBRATION_ENABLED, enabled)

    fun isVibrationChargeEnabled(): Boolean = preferencesHelper.getBoolean(KEY_VIBRATION_CHARGE_ENABLED, false)

    fun setVibrationChargeEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_VIBRATION_CHARGE_ENABLED, enabled)

    fun isVibrationDischargeEnabled(): Boolean = preferencesHelper.getBoolean(KEY_VIBRATION_DISCHARGE_ENABLED, false)

    fun setVibrationDischargeEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_VIBRATION_DISCHARGE_ENABLED, enabled)

    fun isDontDisturbChargeEnabled(): Boolean = preferencesHelper.getBoolean(KEY_DONT_DISTURB_CHARGE_ENABLED, false)

    fun setDontDisturbChargeEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_DONT_DISTURB_CHARGE_ENABLED, enabled)

    fun isDontDisturbDischargeEnabled(): Boolean = preferencesHelper.getBoolean(KEY_DONT_DISTURB_DISCHARGE_ENABLED, false)

    fun setDontDisturbDischargeEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_DONT_DISTURB_DISCHARGE_ENABLED, enabled)

    fun getChargeAlarmPercent(): Int = preferencesHelper.getInt(KEY_CHARGE_ALARM_PERCENT, 80)

    fun setChargeAlarmPercent(percent: Int) {
        preferencesHelper.saveInt(KEY_CHARGE_ALARM_PERCENT, percent)
        _chargeAlarmPercentFlow.update { percent }
    }

    fun getDischargeAlarmPercent(): Int = preferencesHelper.getInt(KEY_DISCHARGE_ALARM_PERCENT, 20)

    fun setDischargeAlarmPercent(percent: Int) = preferencesHelper.saveInt(KEY_DISCHARGE_ALARM_PERCENT, percent)

    fun getDontDisturbChargeFromTime(): String = preferencesHelper.getString(KEY_DONT_DISTURB_CHARGE_FROM_TIME, "22:00") ?: "22:00"

    fun setDontDisturbChargeFromTime(time: String) = preferencesHelper.saveString(KEY_DONT_DISTURB_CHARGE_FROM_TIME, time)

    fun getDontDisturbChargeUntilTime(): String = preferencesHelper.getString(KEY_DONT_DISTURB_CHARGE_UNTIL_TIME, "07:00") ?: "07:00"

    fun setDontDisturbChargeUntilTime(time: String) = preferencesHelper.saveString(KEY_DONT_DISTURB_CHARGE_UNTIL_TIME, time)

    fun getDontDisturbDischargeFromTime(): String = preferencesHelper.getString(KEY_DONT_DISTURB_DISCHARGE_FROM_TIME, "22:00") ?: "22:00"

    fun setDontDisturbDischargeFromTime(time: String) = preferencesHelper.saveString(KEY_DONT_DISTURB_DISCHARGE_FROM_TIME, time)

    fun getDontDisturbDischargeUntilTime(): String = preferencesHelper.getString(KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME, "07:00") ?: "07:00"

    fun setDontDisturbDischargeUntilTime(time: String) = preferencesHelper.saveString(KEY_DONT_DISTURB_DISCHARGE_UNTIL_TIME, time)

    // Charge/Discharge Notification Methods
    fun isChargeNotificationEnabled(): Boolean = preferencesHelper.getBoolean(KEY_CHARGE_NOTIFICATION_ENABLED, false)

    fun setChargeNotificationEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_CHARGE_NOTIFICATION_ENABLED, enabled)

    fun isDischargeNotificationEnabled(): Boolean = preferencesHelper.getBoolean(KEY_DISCHARGE_NOTIFICATION_ENABLED, false)

    fun setDischargeNotificationEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_DISCHARGE_NOTIFICATION_ENABLED, enabled)

    // Language Management Methods
    fun getLanguage(): String = preferencesHelper.getString(KEY_LANGUAGE, "") ?: ""

    fun setLanguage(languageCode: String) = preferencesHelper.saveString(KEY_LANGUAGE, languageCode)

    fun getDefaultLanguage(): String {
        val systemLocale = Locale.getDefault()
        return when (systemLocale.language) {
            "de", "nl", "en", "es", "fr", "it", "hu", "pl", "pt", "ro", "tr", "ru", "uk", "ar", "zh" -> systemLocale.language
            else -> "en" // Fallback to English
        }
    }

    fun setLocale(context: Context, languageCode: String) {
        val locale = Locale(languageCode)
        Locale.setDefault(locale)

        val config = Configuration(context.resources.configuration)
        config.setLocale(locale)

        // Apply configuration to the context's resources
        context.resources.updateConfiguration(config, context.resources.displayMetrics)
    }

    /**
     * Creates a context wrapper with the specified locale.
     * This should be used in attachBaseContext() methods to ensure proper locale propagation.
     */
    fun createLocaleContext(context: Context, languageCode: String): Context {
        val locale = Locale(languageCode)
        val config = Configuration(context.resources.configuration)
        config.setLocale(locale)

        return context.createConfigurationContext(config)
    }

    /**
     * Gets the context wrapper for the currently saved language.
     * Returns the original context if no language is saved.
     */
    fun getLocaleContext(context: Context): Context {
        val savedLanguage = getLanguage()
        return if (savedLanguage.isNotEmpty()) {
            createLocaleContext(context, savedLanguage)
        } else {
            context
        }
    }

    fun isAnimationOverlayEnabled(): Boolean = preferencesHelper.getBoolean(KEY_ANIMATION_OVERLAY_ENABLED, false)
    fun setAnimationOverlayEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_ANIMATION_OVERLAY_ENABLED, enabled)

    fun isAnimationOverlayTimeEnabled(): Boolean = preferencesHelper.getBoolean(KEY_ANIMATION_OVERLAY_TIME_ENABLED, true)
    fun setAnimationOverlayTimeEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_ANIMATION_OVERLAY_TIME_ENABLED, enabled)

    fun getVideoPath(): String? = preferencesHelper.getString(KEY_VIDEO_PATH, "")
    fun setVideoPath(path: String) = preferencesHelper.saveString(KEY_VIDEO_PATH, path)

    // Anti-Thief Methods
    fun isAntiThiefEnabled(): Boolean = preferencesHelper.getBoolean(KEY_ANTI_THIEF_ENABLED, false)
    fun setAntiThiefEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_ANTI_THIEF_ENABLED, enabled)

    fun isAntiThiefSoundEnabled(): Boolean = preferencesHelper.getBoolean(KEY_ANTI_THIEF_SOUND_ENABLED, true)
    fun setAntiThiefSoundEnabled(enabled: Boolean) = preferencesHelper.saveBoolean(KEY_ANTI_THIEF_SOUND_ENABLED, enabled)

    fun isAntiThiefPasswordSet(): Boolean = !getAntiThiefPassword().isNullOrEmpty()
    fun getAntiThiefPassword(): String? = preferencesHelper.getString(KEY_ANTI_THIEF_PASSWORD, "")
    fun setAntiThiefPassword(password: String) = preferencesHelper.saveString(KEY_ANTI_THIEF_PASSWORD, password)
    fun clearAntiThiefPassword() = preferencesHelper.saveString(KEY_ANTI_THIEF_PASSWORD, "")

    // Anti-Thief Alert State
    fun isAntiThiefAlertActive(): Boolean = preferencesHelper.getBoolean(KEY_ANTI_THIEF_ALERT_ACTIVE, false)
    fun setAntiThiefAlertActive(active: Boolean) = preferencesHelper.saveBoolean(KEY_ANTI_THIEF_ALERT_ACTIVE, active)
    
    // Applied Animation URL Methods
    fun getAppliedAnimationUrl(): String? = preferencesHelper.getString(KEY_APPLIED_ANIMATION_URL, "")
    fun setAppliedAnimationUrl(url: String) = preferencesHelper.saveString(KEY_APPLIED_ANIMATION_URL, url)
} 